# frozen_string_literal: true

require_relative "sentry_bench/version"
require_relative "sentry_bench/profiler"
require_relative "sentry_bench/benchmark_transport"
require_relative "sentry_bench/report_generator"
require_relative "sentry_bench/cli"

module SentryBench
  class Error < StandardError; end

  # Main entry point for the benchmarking functionality
  def self.run(options = {})
    profiler = Profiler.new(options)
    profiler.run
  end

  # Convenience method for running with default options
  def self.profile_logging(iterations: 1000, output_dir: 'tmp')
    run(iterations: iterations, output_dir: output_dir)
  end
end
