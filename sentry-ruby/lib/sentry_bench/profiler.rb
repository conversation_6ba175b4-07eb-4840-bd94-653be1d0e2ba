# frozen_string_literal: true

require 'memory_profiler'
require 'fileutils'
require 'json'
require 'time'
require 'logger'

module SentryBench
  class Profiler
    attr_reader :options, :git_info

    def initialize(options = {})
      @options = {
        iterations: 1000,
        output_dir: 'tmp',
        report_file: nil,
        verbose: true
      }.merge(options)
      
      @options[:report_file] ||= File.join(@options[:output_dir], 'sentry_bench_report.html')
      @git_info = get_git_info
    end

    def run
      setup_output_directory
      configure_sentry
      
      puts_verbose "=== Sentry Benchmark (sentry-bench gem) ==="
      puts_verbose "Iterations: #{@options[:iterations]}"
      puts_verbose "Output: #{@options[:report_file]}"
      puts_verbose "Git SHA: #{@git_info[:short_sha]} (#{@git_info[:branch]})"
      puts_verbose

      warmup
      
      start_time = Time.now
      baseline_report, main_report = run_memory_profiling
      end_time = Time.now
      execution_time = end_time - start_time

      transport_stats = get_transport_stats
      
      puts_verbose "Transport Statistics:"
      puts_verbose "  Total events: #{transport_stats[:events]}"
      puts_verbose "  Total envelopes: #{transport_stats[:envelopes]}"
      puts_verbose "  Total log events: #{transport_stats[:log_events]}"
      puts_verbose

      save_benchmark_data(main_report, baseline_report, transport_stats, execution_time)
      generate_report(main_report, baseline_report, transport_stats)
      print_summary(main_report, baseline_report)

      puts_verbose "=== Benchmark Complete ==="
      puts_verbose "Open #{@options[:report_file]} in your browser to view the detailed memory analysis."
      
      @options[:report_file]
    end

    private

    def setup_output_directory
      FileUtils.mkdir_p(@options[:output_dir])
    end

    def configure_sentry
      require 'sentry-ruby'
      
      Sentry.init do |config|
        config.dsn = "dummy://12345:<EMAIL>:3000/sentry/42"
        config.transport.transport_class = SentryBench::BenchmarkTransport
        config.enable_logs = true
        config.sdk_logger = ::Logger.new(nil) # Disable SDK logging to avoid noise
        config.breadcrumbs_logger = []
        config.send_client_reports = false
        config.auto_session_tracking = false
        config.enable_backpressure_handling = false
      end

      unless Sentry.logger
        raise Error, "Sentry.logger is not available. Make sure enable_logs is true."
      end

      puts_verbose "✓ Sentry configured with BenchmarkTransport"
      puts_verbose "✓ Logging enabled: #{Sentry.configuration.enable_logs}"
      puts_verbose
    end

    def warmup
      puts_verbose "Warming up..."
      10.times { |i| Sentry.logger.info("Warmup message #{i}", iteration: i) }
      puts_verbose "✓ Warmup complete"
      puts_verbose
    end

    def run_memory_profiling
      puts_verbose "Starting memory profiling..."

      # Baseline memory measurement
      baseline_report = MemoryProfiler.report do
        # Just initialize without doing anything
        nil
      end

      # Main profiling run
      main_report = MemoryProfiler.report do
        @options[:iterations].times do |i|
          # Vary the log messages to simulate real usage
          case i % 4
          when 0
            Sentry.logger.info("User action completed",
              user_id: i,
              action: "login",
              timestamp: Time.now.to_f,
              session_id: "session_#{i % 100}"
            )
          when 1
            Sentry.logger.warn("API rate limit approaching",
              endpoint: "/api/users",
              current_requests: i * 2,
              limit: 1000,
              user_agent: "TestClient/1.0"
            )
          when 2
            Sentry.logger.error("Database query slow",
              query_time: (i % 10) * 0.1,
              table: "users",
              query_id: "query_#{i}",
              affected_rows: i % 50
            )
          when 3
            Sentry.logger.debug("Cache operation",
              operation: "set",
              key: "user_#{i}",
              ttl: 3600,
              size_bytes: i * 10
            )
          end

          # Add some variability to simulate real-world usage
          if i % 100 == 0
            Sentry.logger.fatal("Critical system error",
              error_code: "SYS_#{i}",
              component: "database",
              severity: "critical"
            )
          end
        end
      end

      puts_verbose "✓ Memory profiling complete"
      puts_verbose

      [baseline_report, main_report]
    end

    def get_transport_stats
      transport = Sentry.get_current_client.transport
      transport.memory_usage_summary
    end

    def save_benchmark_data(main_report, baseline_report, transport_stats, execution_time)
      puts_verbose "Saving benchmark data..."
      
      data_dir = File.join(@options[:output_dir], 'data')
      FileUtils.mkdir_p(data_dir)

      timestamp = Time.now.utc.iso8601
      filename = "#{@git_info[:short_sha]}_#{timestamp.gsub(/[:-]/, '')}.json"
      filepath = File.join(data_dir, filename)

      # Convert MemoryProfiler data to serializable format
      benchmark_data = {
        metadata: {
          git_info: @git_info,
          timestamp: timestamp,
          iterations: @options[:iterations],
          execution_time: execution_time,
          ruby_version: RUBY_VERSION,
          sentry_version: defined?(Sentry::VERSION) ? Sentry::VERSION : "unknown",
          gem_version: SentryBench::VERSION
        },
        memory_profile: {
          total_allocated: main_report.total_allocated,
          total_retained: main_report.total_retained,
          total_allocated_memsize: main_report.total_allocated_memsize,
          total_retained_memsize: main_report.total_retained_memsize,
          allocated_objects_by_class: main_report.allocated_objects_by_class,
          retained_objects_by_class: main_report.retained_objects_by_class,
          allocated_objects_by_location: main_report.allocated_objects_by_location.first(50),
          retained_objects_by_location: main_report.retained_objects_by_location.first(50),
          allocated_memory_by_location: main_report.allocated_memory_by_location.first(50),
          retained_memory_by_location: main_report.retained_memory_by_location.first(50)
        },
        transport_stats: transport_stats
      }

      File.write(filepath, JSON.pretty_generate(benchmark_data))
      puts_verbose "✓ Benchmark data saved: #{filepath}"

      # Also save as latest.json for easy access
      latest_file = File.join(data_dir, 'latest.json')
      File.write(latest_file, JSON.pretty_generate(benchmark_data))
      puts_verbose "✓ Latest benchmark data: #{latest_file}"

      filepath
    end

    def generate_report(main_report, baseline_report, transport_stats)
      puts_verbose "Generating HTML report..."
      
      report_generator = ReportGenerator.new(
        main_report: main_report,
        baseline_report: baseline_report,
        transport_stats: transport_stats,
        iterations: @options[:iterations],
        git_info: @git_info
      )
      
      html_content = report_generator.generate
      File.write(@options[:report_file], html_content)
      
      puts_verbose "✓ HTML report generated: #{@options[:report_file]}"
      puts_verbose
    end

    def print_summary(main_report, baseline_report)
      puts_verbose "Memory Summary:"
      puts_verbose "  Total allocated objects: #{main_report.total_allocated}"
      puts_verbose "  Total retained objects: #{main_report.total_retained}"
      puts_verbose "  Total allocated memory: #{format_bytes(main_report.total_allocated_memsize)}"
      puts_verbose "  Total retained memory: #{format_bytes(main_report.total_retained_memsize)}"
      puts_verbose
    end

    def get_git_info
      {
        sha: sys_command("git rev-parse HEAD"),
        short_sha: sys_command("git rev-parse --short HEAD"),
        branch: sys_command("git rev-parse --abbrev-ref HEAD"),
        commit_message: sys_command("git log -1 --pretty=%B"),
        author: sys_command("git log -1 --pretty=%an"),
        date: sys_command("git log -1 --pretty=%ci")
      }
    rescue => e
      puts_verbose "Warning: Could not get git information: #{e.message}" if @options[:verbose]
      {
        sha: "unknown",
        short_sha: "unknown",
        branch: "unknown",
        commit_message: "unknown",
        author: "unknown",
        date: "unknown"
      }
    end

    def sys_command(command)
      result = `#{command}`.strip
      $?.success? ? result : "unknown"
    end

    def format_bytes(bytes)
      return "0 B" if bytes == 0

      units = ['B', 'KB', 'MB', 'GB']
      size = bytes.to_f
      unit_index = 0

      while size >= 1024 && unit_index < units.length - 1
        size /= 1024
        unit_index += 1
      end

      "#{size.round(2)} #{units[unit_index]}"
    end

    def puts_verbose(message = "")
      puts message if @options[:verbose]
    end
  end
end
