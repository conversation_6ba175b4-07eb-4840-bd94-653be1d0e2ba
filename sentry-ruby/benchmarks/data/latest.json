{"metadata": {"git_info": {"sha": "18bab9048ff30544573d982d7e3070cab821ff26", "short_sha": "18bab904", "branch": "2641-increase-memory-usage-in-logging", "commit_message": "WIP - memory profiler bench for logging", "author": "<PERSON>", "date": "2025-06-06 11:15:58 +0000"}, "timestamp": "2025-06-06T11:22:08Z", "iterations": 200, "execution_time": 0.090668917, "ruby_version": "3.4.2", "sentry_version": "5.24.0"}, "memory_profile": {"total_allocated": 15996, "total_retained": 3197, "total_allocated_memsize": 1647024, "total_retained_memsize": 600744, "allocated_objects_by_class": [{"data": "String", "count": 8144}, {"data": "Hash", "count": 4395}, {"data": "Array", "count": 1782}, {"data": "Time", "count": 856}, {"data": "MatchData", "count": 600}, {"data": "Sentry::LogEvent", "count": 202}, {"data": "Regexp", "count": 8}, {"data": "Symbol", "count": 5}, {"data": "Sentry::<PERSON><PERSON><PERSON>", "count": 2}, {"data": "Sentry::Envelope::Item", "count": 2}], "retained_objects_by_class": [{"data": "Hash", "count": 2588}, {"data": "String", "count": 378}, {"data": "Array", "count": 207}, {"data": "Sentry::LogEvent", "count": 12}, {"data": "Regexp", "count": 8}, {"data": "Sentry::<PERSON><PERSON><PERSON>", "count": 2}, {"data": "Sentry::Envelope::Item", "count": 2}], "allocated_objects_by_location": [{"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 4634}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:65", "count": 3210}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 1766}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 1166}, {"data": "<internal:timev>:265", "count": 656}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 606}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 204}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510", "count": 202}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 202}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222", "count": 202}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 202}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 202}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 202}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:77", "count": 202}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132", "count": 202}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:194", "count": 200}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:231", "count": 200}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:268", "count": 200}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:85", "count": 200}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 200}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 200}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 98}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 98}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 94}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 50}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:107", "count": 50}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 50}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:74", "count": 50}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 50}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:85", "count": 50}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 50}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:96", "count": 50}, {"data": "benchmarks/logging_memory_profile.rb:437", "count": 50}, {"data": "benchmarks/logging_memory_profile.rb:444", "count": 50}, {"data": "benchmarks/logging_memory_profile.rb:451", "count": 50}, {"data": "benchmarks/logging_memory_profile.rb:458", "count": 50}, {"data": "<internal:pack>:25", "count": 12}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 6}, {"data": "benchmarks/logging_memory_profile.rb:469", "count": 3}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/random/formatter.rb:174", "count": 2}, {"data": "/workspace/gems/3.4.2/gems/securerandom-0.4.1/lib/securerandom.rb:71", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:289", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:118", "count": 2}], "retained_objects_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 1766}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 202}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 202}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 200}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 200}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 200}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 50}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 50}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 50}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 50}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 50}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 50}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 50}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 18}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 12}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 12}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 12}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 4}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 2}, {"data": "benchmarks/logging_memory_profile.rb:469", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 1}], "allocated_memory_by_location": [{"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 342976}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 282560}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:65", "count": 248600}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 142832}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 108800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 96960}, {"data": "<internal:timev>:265", "count": 52480}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510", "count": 32320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 32320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222", "count": 32320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 32320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 32320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132", "count": 32320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 16160}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:194", "count": 16000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:268", "count": 16000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 8160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:77", "count": 8080}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:231", "count": 8000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:85", "count": 8000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 8000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:107", "count": 8000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:74", "count": 8000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:85", "count": 8000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:96", "count": 8000}, {"data": "benchmarks/logging_memory_profile.rb:437", "count": 8000}, {"data": "benchmarks/logging_memory_profile.rb:444", "count": 8000}, {"data": "benchmarks/logging_memory_profile.rb:451", "count": 8000}, {"data": "benchmarks/logging_memory_profile.rb:458", "count": 8000}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 3920}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 3920}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 3760}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 2000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 2000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 2000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 2000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 1680}, {"data": "<internal:pack>:25", "count": 800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 720}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:118", "count": 320}, {"data": "benchmarks/logging_memory_profile.rb:468", "count": 320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:671", "count": 256}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/random/formatter.rb:174", "count": 160}, {"data": "/workspace/gems/3.4.2/gems/securerandom-0.4.1/lib/securerandom.rb:71", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 160}, {"data": "benchmarks/logging_memory_profile.rb:469", "count": 120}], "retained_memory_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 282560}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 108800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 104192}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 32320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 32320}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 10272}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 8000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 2000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 2000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 2000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 2000}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 2000}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 2000}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 2000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 1920}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 1920}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 1680}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 960}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 80}, {"data": "benchmarks/logging_memory_profile.rb:469", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 40}]}, "transport_stats": {"events": 0, "envelopes": 2, "log_events": 2, "events_memory": 0, "envelopes_memory": 76, "log_events_memory": 155893}}