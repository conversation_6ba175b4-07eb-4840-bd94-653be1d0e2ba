{"metadata": {"git_info": {"sha": "18bab9048ff30544573d982d7e3070cab821ff26", "short_sha": "18bab904", "branch": "2641-increase-memory-usage-in-logging", "commit_message": "WIP - memory profiler bench for logging", "author": "<PERSON>", "date": "2025-06-06 11:15:58 +0000"}, "timestamp": "2025-06-06T11:20:28Z", "iterations": 100, "execution_time": 0.057098375, "ruby_version": "3.4.2", "sentry_version": "5.24.0"}, "memory_profile": {"total_allocated": 7988, "total_retained": 1608, "total_allocated_memsize": 825608, "total_retained_memsize": 303208, "allocated_objects_by_class": [{"data": "String", "count": 4085}, {"data": "Hash", "count": 2183}, {"data": "Array", "count": 876}, {"data": "Time", "count": 428}, {"data": "MatchData", "count": 300}, {"data": "Sentry::LogEvent", "count": 101}, {"data": "Regexp", "count": 8}, {"data": "Symbol", "count": 5}, {"data": "Sentry::<PERSON><PERSON><PERSON>", "count": 1}, {"data": "Sentry::Envelope::Item", "count": 1}], "retained_objects_by_class": [{"data": "Hash", "count": 1284}, {"data": "String", "count": 199}, {"data": "Array", "count": 104}, {"data": "Sentry::LogEvent", "count": 11}, {"data": "Regexp", "count": 8}, {"data": "Sentry::<PERSON><PERSON><PERSON>", "count": 1}, {"data": "Sentry::Envelope::Item", "count": 1}], "allocated_objects_by_location": [{"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 2334}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:65", "count": 1610}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 868}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 568}, {"data": "<internal:timev>:265", "count": 328}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 303}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 102}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510", "count": 101}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 101}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222", "count": 101}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 101}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 101}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 101}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:77", "count": 101}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132", "count": 101}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:194", "count": 100}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:231", "count": 100}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:268", "count": 100}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:85", "count": 100}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 100}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 100}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 48}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 48}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 47}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 25}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:107", "count": 25}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 25}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:74", "count": 25}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 25}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:85", "count": 25}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 25}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:96", "count": 25}, {"data": "benchmarks/logging_memory_profile.rb:437", "count": 25}, {"data": "benchmarks/logging_memory_profile.rb:444", "count": 25}, {"data": "benchmarks/logging_memory_profile.rb:451", "count": 25}, {"data": "benchmarks/logging_memory_profile.rb:458", "count": 25}, {"data": "<internal:pack>:25", "count": 6}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 3}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/random/formatter.rb:174", "count": 1}, {"data": "/workspace/gems/3.4.2/gems/securerandom-0.4.1/lib/securerandom.rb:71", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:671", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:289", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:118", "count": 1}], "retained_objects_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 868}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 101}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 101}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 100}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 100}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 100}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 25}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 25}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 25}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 25}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 25}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 25}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 25}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 18}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 11}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 11}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 11}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 1}, {"data": "benchmarks/logging_memory_profile.rb:469", "count": 1}], "allocated_memory_by_location": [{"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 178176}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 138880}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:65", "count": 124600}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 68896}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 54400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 48480}, {"data": "<internal:timev>:265", "count": 26240}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510", "count": 16160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 16160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222", "count": 16160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 16160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 16160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132", "count": 16160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 8080}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:194", "count": 8000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:268", "count": 8000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 4080}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:77", "count": 4040}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:231", "count": 4000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:85", "count": 4000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 4000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:107", "count": 4000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:74", "count": 4000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:85", "count": 4000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:96", "count": 4000}, {"data": "benchmarks/logging_memory_profile.rb:437", "count": 4000}, {"data": "benchmarks/logging_memory_profile.rb:444", "count": 4000}, {"data": "benchmarks/logging_memory_profile.rb:451", "count": 4000}, {"data": "benchmarks/logging_memory_profile.rb:458", "count": 4000}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 1920}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 1920}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 1880}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 840}, {"data": "<internal:pack>:25", "count": 400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 360}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:671", "count": 256}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:118", "count": 160}, {"data": "benchmarks/logging_memory_profile.rb:468", "count": 160}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/random/formatter.rb:174", "count": 80}, {"data": "/workspace/gems/3.4.2/gems/securerandom-0.4.1/lib/securerandom.rb:71", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:289", "count": 40}], "retained_memory_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 138880}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 54400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 50176}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 16160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 16160}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 10272}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 4000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 1760}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 1760}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 1000}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 1000}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 1000}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 880}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 840}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 200}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 40}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 40}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 40}, {"data": "benchmarks/logging_memory_profile.rb:469", "count": 40}]}, "transport_stats": {"events": 0, "envelopes": 1, "log_events": 1, "events_memory": 0, "envelopes_memory": 38, "log_events_memory": 77177}}