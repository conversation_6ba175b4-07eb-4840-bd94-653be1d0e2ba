{"metadata": {"git_info": {"sha": "18bab9048ff30544573d982d7e3070cab821ff26", "short_sha": "18bab904", "branch": "2641-increase-memory-usage-in-logging", "commit_message": "WIP - memory profiler bench for logging", "author": "<PERSON>", "date": "2025-06-06 11:15:58 +0000"}, "timestamp": "2025-06-06T11:20:45Z", "iterations": 150, "execution_time": 0.062749333, "ruby_version": "3.4.2", "sentry_version": "5.24.0"}, "memory_profile": {"total_allocated": 8941, "total_retained": 1952, "total_allocated_memsize": 937688, "total_retained_memsize": 343488, "allocated_objects_by_class": [{"data": "String", "count": 4209}, {"data": "Hash", "count": 2693}, {"data": "Array", "count": 1029}, {"data": "Time", "count": 543}, {"data": "MatchData", "count": 300}, {"data": "Sentry::LogEvent", "count": 152}, {"data": "Regexp", "count": 8}, {"data": "Symbol", "count": 5}, {"data": "Sentry::<PERSON><PERSON><PERSON>", "count": 1}, {"data": "Sentry::Envelope::Item", "count": 1}], "retained_objects_by_class": [{"data": "Hash", "count": 1437}, {"data": "String", "count": 288}, {"data": "Array", "count": 155}, {"data": "Sentry::LogEvent", "count": 62}, {"data": "Regexp", "count": 8}, {"data": "Sentry::<PERSON><PERSON><PERSON>", "count": 1}, {"data": "Sentry::Envelope::Item", "count": 1}], "allocated_objects_by_location": [{"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 2334}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:65", "count": 1610}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 868}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 568}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 456}, {"data": "<internal:timev>:265", "count": 443}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 153}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510", "count": 152}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 152}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222", "count": 152}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 152}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 152}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 152}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:77", "count": 152}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132", "count": 152}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:194", "count": 100}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:231", "count": 100}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:268", "count": 100}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:85", "count": 100}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 100}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 100}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 72}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 72}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 70}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 38}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:85", "count": 38}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 38}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:96", "count": 38}, {"data": "benchmarks/logging_memory_profile.rb:437", "count": 38}, {"data": "benchmarks/logging_memory_profile.rb:444", "count": 38}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 37}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:107", "count": 37}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 37}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:74", "count": 37}, {"data": "benchmarks/logging_memory_profile.rb:451", "count": 37}, {"data": "benchmarks/logging_memory_profile.rb:458", "count": 37}, {"data": "<internal:pack>:25", "count": 6}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 3}, {"data": "benchmarks/logging_memory_profile.rb:469", "count": 3}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:118", "count": 2}, {"data": "benchmarks/logging_memory_profile.rb:468", "count": 2}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/random/formatter.rb:174", "count": 1}, {"data": "/workspace/gems/3.4.2/gems/securerandom-0.4.1/lib/securerandom.rb:71", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:671", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:289", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 1}], "retained_objects_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 868}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 152}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 152}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 100}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 100}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 100}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 62}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 62}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 62}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 38}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 38}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 38}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 37}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 37}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 37}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 37}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 18}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 2}, {"data": "benchmarks/logging_memory_profile.rb:469", "count": 2}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 1}], "allocated_memory_by_location": [{"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 178176}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 138880}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:65", "count": 124600}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 72960}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 68896}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 54400}, {"data": "<internal:timev>:265", "count": 35440}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510", "count": 24320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 24320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222", "count": 24320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 24320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 24320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132", "count": 24320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 12160}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:194", "count": 8000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:268", "count": 8000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 6120}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:77", "count": 6080}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:85", "count": 6080}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:96", "count": 6080}, {"data": "benchmarks/logging_memory_profile.rb:437", "count": 6080}, {"data": "benchmarks/logging_memory_profile.rb:444", "count": 6080}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:107", "count": 5920}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:74", "count": 5920}, {"data": "benchmarks/logging_memory_profile.rb:451", "count": 5920}, {"data": "benchmarks/logging_memory_profile.rb:458", "count": 5920}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:231", "count": 4000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:85", "count": 4000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 4000}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 2880}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 2880}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 2800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 1520}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 1520}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 1480}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 1480}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 840}, {"data": "<internal:pack>:25", "count": 400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 360}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:118", "count": 320}, {"data": "benchmarks/logging_memory_profile.rb:468", "count": 320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:671", "count": 256}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 160}, {"data": "benchmarks/logging_memory_profile.rb:469", "count": 120}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/random/formatter.rb:174", "count": 80}, {"data": "/workspace/gems/3.4.2/gems/securerandom-0.4.1/lib/securerandom.rb:71", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 80}], "retained_memory_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:123", "count": 138880}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:57", "count": 54400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:109", "count": 50176}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:53", "count": 24320}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 24320}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 10272}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 9920}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 9920}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 4960}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:75", "count": 4000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 1520}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 1520}, {"data": "benchmarks/logging_memory_profile.rb:441", "count": 1520}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 1480}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 1480}, {"data": "benchmarks/logging_memory_profile.rb:454", "count": 1480}, {"data": "benchmarks/logging_memory_profile.rb:460", "count": 1480}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 840}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 200}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 80}, {"data": "benchmarks/logging_memory_profile.rb:469", "count": 80}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 40}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 40}]}, "transport_stats": {"events": 0, "envelopes": 1, "log_events": 1, "events_memory": 0, "envelopes_memory": 38, "log_events_memory": 77176}}