# sentry-bench

A comprehensive benchmarking tool for measuring memory usage and performance of the Sentry Ruby SDK across different git commits.

## Installation

Add this line to your application's Gemfile:

```ruby
gem 'sentry-bench'
```

And then execute:

    $ bundle install

Or install it yourself as:

    $ gem install sentry-bench

## Usage

### Command Line Interface

The primary way to use sentry-bench is through the command line:

```bash
# Run with default settings (1000 iterations)
sentry-bench

# Run with custom iterations and output directory
sentry-bench -i 5000 -o benchmarks

# Run and automatically open the report in browser
sentry-bench --open

# Run quietly (suppress output)
sentry-bench -q

# Show help
sentry-bench --help
```

### Programmatic Usage

You can also use sentry-bench programmatically in your Ruby code:

```ruby
require 'sentry_bench'

# Run with default options
SentryBench.run

# Run with custom options
SentryBench.run(
  iterations: 5000,
  output_dir: 'benchmarks',
  verbose: true
)

# Convenience method
SentryBench.profile_logging(iterations: 2000, output_dir: 'tmp')
```

## Features

- **Memory Profiling**: Comprehensive memory allocation and retention analysis
- **Git Integration**: Tracks performance across different git commits
- **HTML Reports**: Detailed, interactive reports with charts and recommendations
- **JSON Export**: Machine-readable data for programmatic analysis
- **Transport Simulation**: Uses a benchmark transport to avoid network overhead
- **Performance Recommendations**: Actionable insights for optimization

## Output

The tool generates several types of output:

### HTML Report
- Executive summary with key metrics
- Memory breakdown by object type
- Allocation analysis by code location
- Memory retention analysis
- File-based memory usage analysis
- Performance recommendations
- Raw memory profiler data

### JSON Data Files
- Timestamped benchmark data with git commit information
- Latest benchmark data for easy access
- Structured data for comparison scripts

### Console Output
- Real-time progress and summary statistics
- Git commit information
- Transport statistics

## Options

| Option | Description | Default |
|--------|-------------|---------|
| `-i, --iterations N` | Number of iterations to run | 1000 |
| `-o, --output DIR` | Output directory for reports | tmp |
| `-r, --report FILE` | Custom report file path | output_dir/sentry_bench_report.html |
| `--open` | Open report in browser after generation | false |
| `-q, --quiet` | Run quietly (suppress output) | false |
| `-v, --verbose` | Run with verbose output | true |
| `--version` | Show version | - |
| `-h, --help` | Show help | - |

## Workflow

1. **Setup**: Configures Sentry with a benchmark transport (no network calls)
2. **Warmup**: Runs a small number of operations to ensure classes are loaded
3. **Profiling**: Executes varied logging operations while measuring memory
4. **Analysis**: Analyzes memory allocation and retention patterns
5. **Reporting**: Generates comprehensive HTML report with recommendations
6. **Data Export**: Saves benchmark data with git commit information

## Git Integration

The tool automatically captures git information for each benchmark run:

- Commit SHA (full and short)
- Branch name
- Commit message
- Author
- Date

This enables tracking performance changes across different versions of your codebase.

## Memory Analysis

The tool provides detailed analysis of:

- **Object Allocation**: Which objects are being created and where
- **Memory Retention**: Which objects are being retained (potential leaks)
- **File Analysis**: Which files contribute most to memory usage
- **Class Breakdown**: Memory usage by object type
- **Performance Trends**: Comparison across git commits

## Performance Recommendations

Based on the analysis, the tool provides actionable recommendations:

- String optimization techniques
- Hash allocation optimization
- Log sampling strategies
- Async logging recommendations
- Sentry-specific configuration tips

## Development

After checking out the repo, run `bin/setup` to install dependencies. Then, run `rake spec` to run the tests. You can also run `bin/console` for an interactive prompt that will allow you to experiment.

To install this gem onto your local machine, run `bundle exec rake install`. To release a new version, update the version number in `version.rb`, and then run `bundle exec rake release`, which will create a git tag for the version, push git commits and tags, and push the `.gem` file to [rubygems.org](https://rubygems.org).

## Contributing

Bug reports and pull requests are welcome on GitHub at https://github.com/getsentry/sentry-ruby.

## License

The gem is available as open source under the terms of the [MIT License](https://opensource.org/licenses/MIT).
