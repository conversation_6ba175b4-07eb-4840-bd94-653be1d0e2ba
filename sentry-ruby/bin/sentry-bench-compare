#!/usr/bin/env ruby
# frozen_string_literal: true

# Simple comparison script for sentry-bench results
# Usage: ruby bin/sentry-bench-compare [data_dir]

require 'json'
require 'time'

def format_bytes(bytes)
  return "0 B" if bytes == 0

  units = ['B', 'KB', 'MB', 'GB']
  size = bytes.to_f
  unit_index = 0

  while size >= 1024 && unit_index < units.length - 1
    size /= 1024
    unit_index += 1
  end

  "#{size.round(2)} #{units[unit_index]}"
end

def format_number(number)
  number.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse
end

def load_benchmark_data(data_dir)
  files = Dir.glob(File.join(data_dir, "*.json")).reject { |f| f.end_with?("latest.json") }
  
  files.map do |file|
    data = JSON.parse(File.read(file))
    {
      file: File.basename(file),
      timestamp: Time.parse(data["metadata"]["timestamp"]),
      git_sha: data["metadata"]["git_info"]["short_sha"],
      branch: data["metadata"]["git_info"]["branch"],
      commit_message: data["metadata"]["git_info"]["commit_message"].split("\n").first,
      iterations: data["metadata"]["iterations"],
      total_allocated: data["memory_profile"]["total_allocated"],
      total_retained: data["memory_profile"]["total_retained"],
      total_allocated_memsize: data["memory_profile"]["total_allocated_memsize"],
      total_retained_memsize: data["memory_profile"]["total_retained_memsize"],
      execution_time: data["metadata"]["execution_time"],
      ruby_version: data["metadata"]["ruby_version"],
      sentry_version: data["metadata"]["sentry_version"]
    }
  end.sort_by { |d| d[:timestamp] }
end

def compare_benchmarks(benchmarks)
  puts "=== Sentry Benchmark Comparison ==="
  puts
  
  if benchmarks.empty?
    puts "No benchmark data found."
    return
  end

  puts "Found #{benchmarks.length} benchmark(s):"
  puts

  # Table header
  printf "%-12s %-10s %-8s %-12s %-12s %-12s %-12s\n",
         "Git SHA", "Branch", "Iter", "Allocated", "Retained", "Alloc Mem", "Ret Mem"
  puts "-" * 90

  benchmarks.each do |benchmark|
    printf "%-12s %-10s %-8s %-12s %-12s %-12s %-12s\n",
           benchmark[:git_sha],
           benchmark[:branch][0..9],
           format_number(benchmark[:iterations]),
           format_number(benchmark[:total_allocated]),
           format_number(benchmark[:total_retained]),
           format_bytes(benchmark[:total_allocated_memsize]),
           format_bytes(benchmark[:total_retained_memsize])
  end

  puts
  puts "=== Performance Trends ==="
  puts

  if benchmarks.length > 1
    baseline = benchmarks.first
    latest = benchmarks.last

    puts "Comparing latest (#{latest[:git_sha]}) vs baseline (#{baseline[:git_sha]}):"
    puts

    # Calculate changes
    allocated_change = ((latest[:total_allocated].to_f / baseline[:total_allocated] - 1) * 100).round(2)
    retained_change = ((latest[:total_retained].to_f / baseline[:total_retained] - 1) * 100).round(2)
    memory_change = ((latest[:total_allocated_memsize].to_f / baseline[:total_allocated_memsize] - 1) * 100).round(2)

    puts "Object Allocation: #{format_change(allocated_change)}"
    puts "Object Retention: #{format_change(retained_change)}"
    puts "Memory Usage: #{format_change(memory_change)}"
    puts

    # Show per-iteration metrics
    baseline_per_iter = baseline[:total_allocated_memsize] / baseline[:iterations]
    latest_per_iter = latest[:total_allocated_memsize] / latest[:iterations]
    per_iter_change = ((latest_per_iter.to_f / baseline_per_iter - 1) * 100).round(2)

    puts "Memory per iteration:"
    puts "  Baseline: #{format_bytes(baseline_per_iter)}"
    puts "  Latest:   #{format_bytes(latest_per_iter)}"
    puts "  Change:   #{format_change(per_iter_change)}"
  else
    puts "Only one benchmark found. Run more benchmarks to see trends."
  end

  puts
  puts "=== Recent Commits ==="
  puts

  benchmarks.last(5).each do |benchmark|
    puts "#{benchmark[:git_sha]} (#{benchmark[:timestamp].strftime('%Y-%m-%d %H:%M')})"
    puts "  #{benchmark[:commit_message]}"
    puts "  Memory: #{format_bytes(benchmark[:total_allocated_memsize])} allocated, #{format_bytes(benchmark[:total_retained_memsize])} retained"
    puts
  end
end

def format_change(percentage)
  if percentage > 0
    "📈 +#{percentage}% (increase)"
  elsif percentage < 0
    "📉 #{percentage}% (decrease)"
  else
    "➡️  0% (no change)"
  end
end

# Main execution
data_dir = ARGV[0] || "tmp/data"

unless Dir.exist?(data_dir)
  puts "Error: Data directory '#{data_dir}' not found."
  puts "Usage: #{$0} [data_dir]"
  exit 1
end

benchmarks = load_benchmark_data(data_dir)
compare_benchmarks(benchmarks)
